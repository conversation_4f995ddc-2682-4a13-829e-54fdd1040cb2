<template>
  <template v-if="userRoleIsBusiness">
    <view
      class="flex items-stretch c-#333333 text-26rpx my-30rpx py-30rpx bg-white rounded-[40rpx] px-42rpx gap-40rpx"
    >
      <view class="flex flex-col gap-30rpx flex-1">
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoPosition" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex">{{ customBusinessCardInfo?.infoOne }}</text>
        </view>
        <view class="flex items-center gap-10rpx">
          <wd-img :src="birthdayIcon" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ customBusinessCardInfo?.infoThree }}
          </text>
        </view>
      </view>
      <view class="flex flex-col gap-30rpx flex-1">
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoCompanySalary" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ customBusinessCardInfo?.infoTwo }}
          </text>
        </view>
        <view v-if="customBusinessCardInfo?.infoFour" class="flex items-center gap-10rpx">
          <wd-img :src="infoAge" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ customBusinessCardInfo.infoFour }}
          </text>
        </view>
      </view>
    </view>
  </template>
  <template v-else>
    <view
      class="flex items-center c-#333333 text-26rpx my-30rpx bg-white rounded-[40rpx] h-156rpx px-42rpx gap-40rpx"
    >
      <view class="flex flex-col flex-justify-center gap-30rpx flex-1">
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoPosition" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex">{{ customCardInfo.positionName }}</text>
        </view>
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoWelfare" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ customCardInfo.positionBenefitList?.join(' · ') }}
          </text>
        </view>
      </view>
      <view class="flex flex-col flex-justify-center gap-30rpx flex-1">
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoCompanySalary" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{
              formatSalary(
                customCardInfo.workSalaryBegin,
                customCardInfo.workSalaryEnd,
                customCardInfo.salaryMonths,
                '月',
              )
            }}
          </text>
        </view>
        <view class="flex items-center gap-10rpx">
          <wd-img :src="infoCompanyAddress" height="34rpx" width="34rpx" />
          <text class="line-clamp-1 flex-1">
            {{ formatAddress }}
          </text>
        </view>
      </view>
    </view>
  </template>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { formatSalary } from '@/utils'
import { positionInfoQueryIMCardInfoById } from '@/service/positionInfo'
import { hrResumeQueryIMCardInfoById } from '@/service/hrResume'
import type { hrResumeQueryIMCardInfoByIdInt } from '@/service/hrResume/types'
import infoPosition from '@/ChatUIKit/static/info-position.png'
import infoCompanyAddress from '@/ChatUIKit/static/info-company-address.png'
import infoCompanySalary from '@/ChatUIKit/static/info-company-salary.png'
import infoWelfare from '@/ChatUIKit/static/info-welfare.png'
import infoAge from '@/ChatUIKit/static/info-age.png'
import infoSchoolEducation from '@/ChatUIKit/static/info-school-education.png'
import birthdayIcon from '@/ChatUIKit/static/birthday_icon.png'

interface propsInterface {
  ext: Api.IM.UserBusinessExtInfo
}

defineOptions({
  name: 'ChatInfoCard',
})

const props = withDefaults(defineProps<propsInterface>(), {})
const propsExt = computed(() => props.ext)

const { userIntel, getUserIsLogin, userRoleIsBusiness } = useUserInfo()
const { customCardInfo } = useIMConversation()

const customBusinessCardInfo = ref<hrResumeQueryIMCardInfoByIdInt>({})
const formatAddress = computed(() => {
  const { provinceName, cityName, districtName } = customCardInfo.value
  const isDirectMunicipality = provinceName === cityName
  const addressParts = isDirectMunicipality
    ? [provinceName, districtName]
    : [provinceName, cityName, districtName]
  return addressParts.filter(Boolean).join('')
})

async function fetchCardInfo() {
  try {
    const hrUserId = userRoleIsBusiness.value ? userIntel.value.userId : propsExt.value.hrUserId
    const userId = userRoleIsBusiness.value ? propsExt.value.cUserId : userIntel.value.userId
    if (userRoleIsBusiness.value) {
      const { data } = await hrResumeQueryIMCardInfoById(
        {
          userId,
        },
        {
          custom: {
            toast: true,
            catch: true,
          },
        },
      )
      customBusinessCardInfo.value = data
      customCardInfo.value.positionInfoId = data.positionId
    } else {
      const { data } = await positionInfoQueryIMCardInfoById(
        {
          userId,
          hrUserId,
        },
        {
          custom: {
            toast: true,
            catch: true,
          },
        },
      )
      customCardInfo.value = data
    }
  } catch (error) {
    console.error('Error fetching card info:', error)
  }
}

watch(
  [() => getUserIsLogin.value],
  ([login]) => {
    if (login) {
      fetchCardInfo()
    }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
//
</style>
